package com.aispeech.hybridspeech.session

import android.content.Context
import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.IHybridSpeechConfigProvider
import com.aispeech.hybridspeech.IRecordingSessionCallback
import com.aispeech.hybridspeech.RecordingConfig
import com.aispeech.hybridspeech.RecordingResultInfo
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.hybridspeech.core.CoroutineScopeManager
import com.aispeech.hybridspeech.core.MainAppController
import com.aispeech.hybridspeech.core.safeCollect
import com.aispeech.hybridspeech.session.persistence.SessionPersistenceManager
import com.aispeech.hybridspeech.session.persistence.SessionState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import java.util.concurrent.atomic.AtomicLong

/**
 * 录音会话实现类
 * 
 * 职责：
 * 1. 管理单个录音会话的生命周期
 * 2. 处理录音控制操作（开始、暂停、恢复、停止）
 * 3. 管理事件订阅和回调通知
 * 4. 协调与 MainAppController 的交互
 * 5. 支持会话状态持久化和恢复
 */
class RecordingSessionImpl(
  private val context: Context,
  private val sessionId: String,
  private val clientCallback: IRecordingSessionCallback,
  private val onRelease: (String) -> Unit,
  private val onStatusChanged: (String, Int) -> Unit = { _, _ -> },
  private val persistenceManager: SessionPersistenceManager
) {
  private val TAG = "RecordingSession-$sessionId"

  // 核心组件
  private val mainController = MainAppController(context)
  private val sessionScopeDelegate = CoroutineScopeManager.createModuleScopeDelegate(
    moduleName = TAG,
    parentScope = CoroutineScopeManager.getMainParentScope()
  )
  private val sessionScope: CoroutineScope = sessionScopeDelegate

  // 状态管理
  private val sessionStateManager = SessionStateManager(sessionId, persistenceManager)
  private val eventSubscriptionManager = SessionEventSubscriptionManager(
    sessionId = sessionId,
    sessionScope = sessionScope,
    mainController = mainController,
    clientCallback = clientCallback,
    onStatusChanged = onStatusChanged,
    onSessionStateChanged = { state -> sessionStateManager.updateState(state) }
  )

  // 会话元数据
  private val creationTime = AtomicLong(System.currentTimeMillis())
  
  @Volatile
  private var isReleased = false

  init {
    AILog.i(TAG, "Session instance created.")
    // 保存初始状态
    sessionStateManager.saveInitialState(
      config = null, // 将在 start 时设置
      creationTime = creationTime.get()
    )
  }

  /**
   * 启动录音会话
   */
  fun start(config: RecordingConfig, provider: IHybridSpeechConfigProvider?) {
    if (isReleased) {
      AILog.w(TAG, "Attempted to start a released session")
      return
    }

    AILog.i(TAG, "Starting session...")
    
    // 更新配置到状态管理器
    sessionStateManager.updateConfig(config)
    
    // 开始事件订阅
    eventSubscriptionManager.subscribeToEvents()

    // 创建启动回调
    val startCallback = SessionStartCallbackHandler(
      sessionId = sessionId,
      clientCallback = clientCallback,
      onStatusChanged = onStatusChanged,
      onSessionStarted = { sessionStateManager.markAsStarted() },
      onSessionFailed = { 
        sessionStateManager.markAsFailed()
        release() 
      }
    )

    // 启动录音
    if (provider != null) {
      mainController.startRecordingWithProvider(config, provider, startCallback)
    } else {
      mainController.startRecordingWithConfigAsync(config, startCallback)
    }
  }

  /**
   * 暂停录音
   */
  fun pause() {
    if (isReleased) {
      AILog.w(TAG, "Attempted to pause a released session")
      return
    }

    AILog.i(TAG, "Pausing session...")
    sessionStateManager.markAsPaused()
    
    val pauseCallback = SessionPauseCallbackHandler(
      sessionId = sessionId,
      clientCallback = clientCallback,
      onStatusChanged = onStatusChanged
    )
    
    mainController.pauseRecordingAsync(pauseCallback)
  }

  /**
   * 恢复录音
   */
  fun resume() {
    if (isReleased) {
      AILog.w(TAG, "Attempted to resume a released session")
      return
    }

    AILog.i(TAG, "Resuming session...")
    sessionStateManager.markAsResumed()
    
    val resumeCallback = SessionResumeCallbackHandler(
      sessionId = sessionId,
      clientCallback = clientCallback,
      onStatusChanged = onStatusChanged
    )
    
    mainController.resumeRecordingAsync(resumeCallback)
  }

  /**
   * 停止录音
   */
  fun stop() {
    if (isReleased) {
      AILog.w(TAG, "Attempted to stop a released session")
      return
    }

    AILog.i(TAG, "Stopping session...")
    sessionStateManager.markAsStopping()
    
    val stopCallback = SessionStopCallbackHandler(
      sessionId = sessionId,
      clientCallback = clientCallback,
      onStatusChanged = onStatusChanged,
      onSessionStopped = { result ->
        sessionStateManager.markAsCompleted(result)
        release()
      }
    )
    
    mainController.stopRecordingAsync(stopCallback)
  }

  /**
   * 释放会话资源
   */
  fun release() {
    if (isReleased) {
      AILog.w(TAG, "Session already released")
      return
    }

    AILog.i(TAG, "Releasing session...")
    isReleased = true

    // 停止事件订阅
    eventSubscriptionManager.unsubscribeFromEvents()

    // 释放主控制器
    mainController.release()

    // 关闭协程作用域
    serviceScopeDelegate.shutdown("Session released")

    // 清理持久化状态
    sessionStateManager.cleanup()

    // 通知服务清理
    onRelease(sessionId)

    AILog.i(TAG, "Session released")
  }

  // === 状态查询方法 ===

  fun getStatus(): Int = if (isReleased) ServiceStatus.IDLE else mainController.getCurrentStatus()
  
  fun getRecordingDuration(): Long = if (isReleased) 0L else mainController.getRecordingDuration()
  
  fun getCreationTime(): Long = creationTime.get()
  
  fun getSessionState(): SessionState? = sessionStateManager.getCurrentState()

  // === 恢复相关方法 ===

  /**
   * 从持久化状态恢复会话
   */
  fun restoreFromState(state: SessionState): Boolean {
    if (isReleased) {
      AILog.w(TAG, "Cannot restore a released session")
      return false
    }

    return try {
      AILog.i(TAG, "Restoring session from state: ${state.status}")
      sessionStateManager.restoreState(state)
      
      // 根据状态决定是否需要重新启动
      when (state.status) {
        ServiceStatus.RECORDING -> {
          // 重新开始录音
          state.config?.let { config ->
            start(config, null)
          } ?: false
        }
        ServiceStatus.PAUSED -> {
          // 恢复到暂停状态
          state.config?.let { config ->
            start(config, null)
            // 启动后立即暂停
            sessionScope.launch {
              pause()
            }
          } ?: false
        }
        else -> true // 其他状态不需要特殊处理
      }
      true
    } catch (e: Exception) {
      AILog.e(TAG, "Failed to restore session from state", e)
      false
    }
  }

  /**
   * 检查会话是否可以恢复
   */
  fun canRestore(): Boolean {
    return !isReleased && sessionStateManager.hasValidState()
  }
}
