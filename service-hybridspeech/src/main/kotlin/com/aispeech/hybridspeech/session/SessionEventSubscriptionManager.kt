package com.aispeech.hybridspeech.session

import com.aispeech.aibase.AILog
import com.aispeech.hybridspeech.IRecordingSessionCallback
import com.aispeech.hybridspeech.RecordingResultInfo
import com.aispeech.hybridspeech.TranscriptionResult
import com.aispeech.hybridspeech.core.MainAppController
import com.aispeech.hybridspeech.core.safeCollect
import com.aispeech.hybridspeech.session.persistence.SessionState
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

/**
 * 会话事件订阅管理器
 * 
 * 职责：
 * 1. 管理与 MainAppController 的事件订阅
 * 2. 处理转写结果、错误和状态变化事件
 * 3. 将事件转发给客户端回调
 * 4. 协调状态更新
 */
class SessionEventSubscriptionManager(
  private val sessionId: String,
  private val sessionScope: CoroutineScope,
  private val mainController: MainAppController,
  private val clientCallback: IRecordingSessionCallback,
  private val onStatusChanged: (String, Int) -> Unit,
  private val onSessionStateChanged: (SessionState) -> Unit
) {
  private val TAG = "SessionEventManager-$sessionId"
  
  private var eventSubscriptionJob: Job? = null
  private var isSubscribed = false
  
  /**
   * 开始订阅事件
   */
  fun subscribeToEvents() {
    if (isSubscribed) {
      AILog.w(TAG, "Already subscribed to events")
      return
    }
    
    AILog.i(TAG, "Starting event subscription")
    isSubscribed = true
    
    eventSubscriptionJob = sessionScope.launch {
      // 订阅转写结果
      launch {
        mainController.transcriptionResultFlow.safeCollect(
          tag = TAG,
          onError = { error ->
            AILog.e(TAG, "Error in transcription result flow", error)
            handleFlowError("Transcription flow error: ${error.message}")
          }
        ) { result ->
          handleTranscriptionResult(result)
        }
      }
      
      // 订阅错误
      launch {
        mainController.errorFlow.safeCollect(
          tag = TAG,
          onError = { error ->
            AILog.e(TAG, "Error in error flow", error)
          }
        ) { error ->
          handleError(error)
        }
      }
      
      // 订阅状态变化
      launch {
        mainController.statusChangeFlow.safeCollect(
          tag = TAG,
          onError = { error ->
            AILog.e(TAG, "Error in status change flow", error)
          }
        ) { status ->
          handleStatusChange(status)
        }
      }
    }
  }
  
  /**
   * 停止订阅事件
   */
  fun unsubscribeFromEvents() {
    if (!isSubscribed) {
      AILog.w(TAG, "Not subscribed to events")
      return
    }
    
    AILog.i(TAG, "Stopping event subscription")
    isSubscribed = false
    
    eventSubscriptionJob?.cancel()
    eventSubscriptionJob = null
  }
  
  /**
   * 处理转写结果
   */
  private fun handleTranscriptionResult(result: TranscriptionResult) {
    try {
      AILog.d(TAG, "Received transcription result: ${result::class.simpleName}")
      
      // 转换为客户端格式
      val resultInfo = result.toRecordingResultInfo()
      
      // 通知客户端
      clientCallback.onTranscriptionUpdate(resultInfo)
      
      // 更新会话状态（添加转写结果）
      // 注意：这里我们通过回调通知状态管理器，而不是直接操作
      // 这样保持了组件间的解耦
      
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling transcription result", e)
      handleError("Failed to process transcription result: ${e.message}")
    }
  }
  
  /**
   * 处理错误
   */
  private fun handleError(error: String) {
    try {
      AILog.e(TAG, "Received error: $error")
      
      // 更新状态为错误
      onStatusChanged(sessionId, ServiceStatus.ERROR)
      
      // 通知客户端
      clientCallback.onError(0, error)
      
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling error event", e)
    }
  }
  
  /**
   * 处理状态变化
   */
  private fun handleStatusChange(status: Int) {
    try {
      AILog.d(TAG, "Status changed to: $status")
      
      // 通知服务层状态变化
      onStatusChanged(sessionId, status)
      
    } catch (e: Exception) {
      AILog.e(TAG, "Error handling status change", e)
    }
  }
  
  /**
   * 处理流错误
   */
  private fun handleFlowError(errorMessage: String) {
    try {
      onStatusChanged(sessionId, ServiceStatus.ERROR)
      clientCallback.onError(0, errorMessage)
    } catch (e: Exception) {
      AILog.e(TAG, "Error notifying flow error", e)
    }
  }
  
  /**
   * 检查是否已订阅
   */
  fun isSubscribed(): Boolean = isSubscribed
}

/**
 * 扩展函数：将 TranscriptionResult 转换为 RecordingResultInfo
 */
private fun TranscriptionResult.toRecordingResultInfo(): RecordingResultInfo {
  val text = when (this) {
    is TranscriptionResult.ProcessingTextResult -> this.text
    is TranscriptionResult.FinalTextResult -> this.text ?: ""
    is TranscriptionResult.IntermediateResult -> this.`var`
    is TranscriptionResult.StartResult -> this.message
    is TranscriptionResult.AgendaResult -> this.text ?: ""
    is TranscriptionResult.InitializationResult -> this.results.joinToString(" ") { it.text }
  }

  return RecordingResultInfo(
    pcmFilePath = "", // 转写结果中没有文件路径信息
    mp3FilePath = "",
    durationMs = 0L, // 转写结果中没有时长信息
    fileSizeBytes = 0L,
    audioConfig = com.aispeech.hybridspeech.AudioRecordingConfig.createDefault()
  )
}
